const ColorByTheme = {
  blue: ["#0c7dd8", "#61bbfb"],
  orange: ["#ec8e08", "#f0b36d"],
  red: ["#d01a38", "#e75454"],
};

let svg = null;
let container = "";
const defaultTheme = "blue";

function draw(svgContainer, nodeInfo = {}) {
  const { nodeSize = "200*200", nodeText = "" } = nodeInfo;
  const [width, height] = nodeSize.split("*").map((item) => +item);

  container = svgContainer;
  if (container.empty()) {
    throw new Error("Container is required");
  }

  svg = container
    .append("svg")
    .attr("width", width)
    .attr("height", height)
    .attr("viewBox", "0 0 30 45");

  // 圆柱体主体
  svg
    .append("rect")
    .attr("x", "0")
    .attr("y", "8")
    .attr("width", "30")
    .attr("height", "30")
    .style("stroke", "#fff")
    .style("stroke-width", "0.5")
    .style("fill", "#94acc8");

  // 圆柱体底部椭圆
  svg
    .append("ellipse")
    .attr("cx", "15")
    .attr("cy", "8")
    .attr("rx", "15")
    .attr("ry", "5")
    .style("stroke", "#fff")
    .style("stroke-width", "0.5")
    .style("fill", "#94acc8");

  // 填充顶部椭圆
  svg
    .append("path")
    .attr("class", "silo-top")
    .style("stroke", "#fff")
    .style("stroke-width", "0.5");

  // 填充主体
  svg
    .append("path")
    .attr("class", "silo-body")
    .style("stroke", "#fff")
    .style("stroke-width", "0.5");

  // 顶部透明覆盖层
  svg
    .append("path")
    .attr("d", "M 0 8 a 15 5 0 1 0 30 0 a 15 5 0 1 0 -30 0 z")
    .style("stroke", "#fff")
    .style("stroke-width", "0.5")
    .style("fill", "#fff")
    .style("fill-opacity", "0.3");

  // 文本标签
  svg
    .append("text")
    .attr("x", 15)
    .attr("y", 30)
    .style("fill", "#fff")
    .style("font-size", "8px")
    .style("text-anchor", "middle")
    .text(nodeText);

  update();
}

function d1(value) {
  return `M 0 ${8 + 30 * (1 - value)} a 15 5 0 1 0 30 0 a 15 5 0 1 0 -30 0 z`;
}

function d2(value) {
  return `M 0 38 a 15 5 0 1 0 30 0 l 0 -${30 * value} a 15 5 0 1 1 -30 0 z `;
}

const data = {
  value: 0,
  theme: "blue",
};

const themeOptions = {
  0: "blue",
  1: "orange",
  2: "red",
};

// tempwarn 0 银色 1 橙色 2 红色
// stocknum 实时库存数据量
// maxstocknum 最大库存数据量
let maxstocknum = 1;

function formatData(d = []) {
  // 先把最大库存拿出来
  d.forEach((item) => {
    if (item.column === "maxstocknum") {
      maxstocknum = item.value;
    }
  });

  d.forEach((item) => {
    if (item.column === "stocknum") {
      data.value = Math.min(1, item.value / maxstocknum);
    }
    if (item.column === "tempwarn") {
      data.theme = themeOptions[+item.value];
    }
  });
  return data;
}

function update(d = []) {
  const { value, theme } = formatData(d);

  svg
    .select(".silo-top")
    .attr("d", d1(value))
    .style("fill", ColorByTheme[theme][1]);

  svg
    .select(".silo-body")
    .attr("d", d2(value))
    .style("fill", ColorByTheme[theme][0]);
}

return {
  draw,
  update,
};
