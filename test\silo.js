/**
 * 圆柱体
 * @param {HTMLElement} container - 容器元素
 * @param {string} width - 圆柱体宽度，默认为200
 * @param {string} height - 圆柱体高度，默认为200
 * @param {string} name - 圆柱体名称
 * @param {number} value - 圆柱体填充高度，默认为0, 值区间0-1
 * @param {string} theme - 圆柱体填充颜色，默认为"sliver"。支持"sliver"、"orange"、"red"
 */
function initSilo(
  container,
  name,
  value = 0,
  theme = "sliver",
  width = 200,
  height = 200
) {
  var svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  svg.setAttribute("width", width);
  svg.setAttribute("height", height);
  svg.setAttribute("viewBox", "0 0 30 45");

  var rect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
  rect.setAttribute("x", "0");
  rect.setAttribute("y", "8");
  rect.setAttribute("width", "30");
  rect.setAttribute("height", "30");
  rect.style.stroke = "#fff";
  rect.style.strokeWidth = "0.5";
  rect.style.fill = "#94acc8";
  svg.appendChild(rect);

  var ellipse = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "ellipse"
  );
  ellipse.setAttribute("cx", "15");
  ellipse.setAttribute("cy", "8");
  ellipse.setAttribute("rx", "15");
  ellipse.setAttribute("ry", "5");
  ellipse.style.stroke = "#fff";
  ellipse.style.strokeWidth = "0.5";
  ellipse.style.fill = "#94acc8";
  svg.appendChild(ellipse);

  var pathTop = document.createElementNS("http://www.w3.org/2000/svg", "path");
  pathTop.setAttribute("d", d1(value));
  pathTop.setAttribute("class", `silo-top-${theme}`);
  pathTop.style.stroke = "#fff";
  pathTop.style.strokeWidth = "0.5";
  svg.appendChild(pathTop);

  var siloPath = document.createElementNS("http://www.w3.org/2000/svg", "path");
  siloPath.setAttribute("d", d2(value));
  siloPath.setAttribute("class", `silo-${theme}`);
  siloPath.style.stroke = "#fff";
  siloPath.style.strokeWidth = "0.5";
  svg.appendChild(siloPath);

  var path = document.createElementNS("http://www.w3.org/2000/svg", "path");
  path.setAttribute("d", "M 0 8 a 15 5 0 1 0 30 0 a 15 5 0 1 0 -30 0 z");
  path.style.stroke = "#fff";
  path.style.strokeWidth = "0.5";
  path.style.fill = "#fff";
  path.style.fillOpacity = "0.3";
  svg.appendChild(path);

  var text = document.createElementNS("http://www.w3.org/2000/svg", "text");
  text.setAttribute("x", 15);
  text.setAttribute("y", 30);
  text.style.fill = "#fff";
  text.style.fontSize = "8px";
  text.style.textAnchor = "middle";
  text.innerHTML = name;
  svg.appendChild(text);

  container.appendChild(svg);

  renderLink();
}

function d1(value) {
  return `M 0 ${8 + 30 * (1 - value)} a 15 5 0 1 0 30 0 a 15 5 0 1 0 -30 0 z`;
}

function d2(value) {
  return `M 0 38 a 15 5 0 1 0 30 0 l 0 -${30 * value} a 15 5 0 1 1 -30 0 z `;
}
function renderLink() {
  var siloSliver = document.getElementsByClassName("silo-sliver");
  if (siloSliver) {
    for (var i = 0; i < siloSliver.length; i++) {
      siloSliver[i].style.fill = "#0c7dd8";
    }
  }

  var siloOrange = document.getElementsByClassName("silo-orange");
  if (siloOrange) {
    for (var i = 0; i < siloOrange.length; i++) {
      siloOrange[i].style.fill = "#ec8e08";
    }
  }

  var siloRed = document.getElementsByClassName("silo-red");
  if (siloRed) {
    for (var i = 0; i < siloRed.length; i++) {
      siloRed[i].style.fill = "#d01a38";
    }
  }

  var siloTopSliver = document.getElementsByClassName("silo-top-sliver");
  if (siloTopSliver) {
    for (var i = 0; i < siloTopSliver.length; i++) {
      siloTopSliver[i].style.fill = "#61bbfb";
    }
  }

  var siloTopOrange = document.getElementsByClassName("silo-top-orange");
  if (siloTopOrange) {
    for (var i = 0; i < siloTopOrange.length; i++) {
      siloTopOrange[i].style.fill = "#f0b36d";
    }
  }

  var siloTopRed = document.getElementsByClassName("silo-top-red");
  if (siloTopRed) {
    for (var i = 0; i < siloTopRed.length; i++) {
      siloTopRed[i].style.fill = "#e75454";
    }
  }
}
