<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>silo 图形展示</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #333;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        padding: 0;
      }
      .container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
      }
      .silo-container {
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 10px;
        padding: 20px;
        text-align: center;
      }
      .silo-title {
        color: white;
        margin-bottom: 10px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="silo-container">
        <h3 class="silo-title">银色主题</h3>
        <div id="silo-sliver"></div>
      </div>

      <div class="silo-container">
        <h3 class="silo-title">橙色主题</h3>
        <div id="silo-orange"></div>
      </div>

      <div class="silo-container">
        <h3 class="silo-title">红色主题</h3>
        <div id="silo-red"></div>
      </div>
    </div>

    <script src="silo.js"></script>
    <script>
      // 页面加载完成后初始化所有图形
      document.addEventListener("DOMContentLoaded", function () {
        // 初始化小尺寸银色主题图形，值为0.3
        initSilo(
          document.getElementById("silo-sliver"),
          "设备A",
          0.3,
          "sliver"
        );

        // 初始化中尺寸橙色主题图形，值为0.6
        initSilo(
          document.getElementById("silo-orange"),
          "设备B",
          0.6,
          "orange"
        );

        // 初始化中尺寸红色主题图形，值为0.5
        initSilo(document.getElementById("silo-red"), "设备C", 1, "red");
      });
    </script>
  </body>
</html>
