const ColorByTheme = {
  green: ["#00896b", "#39cda6", "#005545"],
  orange: ["#ec8e08", "#f0b36d", "#af6816"],
  red: ["#d01a38", "#e75454", "#970404"],
  blue: ["#61bbfb", "#396e98", "#0c7dd8"],
};
// 尺寸选项 s m l
const size = "s";
let svg = null;

let container = "";
function draw(svgContainer, nodeInfo = {}) {
  const { nodeSize = "200*200", nodeText = "" } = nodeInfo;
  const [width, height] = nodeSize.split("*").map((item) => +item);

  container = svgContainer;
  if (container.empty()) {
    throw new Error("Container is required");
  }
  svg = container
    .append("svg")
    .attr("width", width)
    .attr("height", height)
    .attr("data-size", size)
    .attr("viewBox", vb(size));

  const path = svg.append("path").attr("class", `inner inner1`);

  const path2 = svg.append("path").attr("class", "outer outer1");

  const path3 = svg.append("path").attr("class", "outer outer2");

  const path4 = svg.append("path").attr("class", `inner inner2`);
  const path5 = svg.append("path").attr("class", `inner inner3`);

  const text = svg
    .append("text")
    .attr("x", textPosition(size)[0])
    .attr("y", textPosition(size)[1])
    .style("fill", "#fff")
    .style("font-size", "2px")
    .style("text-anchor", "middle")
    .text(nodeText);

  update();
}

function vb(size) {
  return size === "s"
    ? "6 2.5 9 7.5"
    : size === "m"
    ? "6 2.5 13 7.5"
    : "6 2.5 17 7.5";
}

function textPosition(size) {
  return size === "s" ? [10, 8.5] : size === "m" ? [11, 9.5] : [12.5, 9.5];
}

function outer1D(size) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return `M${bp[0][0]} ${bp[0][1]} ${tp[0][0]} ${tp[0][1]} ${tp[1][0]} ${tp[1][1]} ${bp[1][0]} ${bp[1][1]} ${bp[0][0]} ${bp[0][1]}M${bp[1][0]} ${bp[1][1]} ${tp[1][0]} ${tp[1][1]} ${tp[2][0]} ${tp[2][1]} ${bp[2][0]} ${bp[2][1]} ${bp[1][0]} ${bp[1][1]}M${bp[0][0]} ${bp[0][1]} ${bp[3][0]} ${bp[3][1]}M${bp[2][0]} ${bp[2][1]} ${bp[3][0]} ${bp[3][1]}M${tp[3][0]} ${tp[3][1]} ${bp[3][0]} ${bp[3][1]}M${tp[0][0]} ${tp[0][1]} ${tp[3][0]} ${tp[3][1]}M${tp[2][0]} ${tp[2][1]} ${tp[3][0]} ${tp[3][1]}`;
}

function outer2D(size) {
  const tp = points[`${size}t`];
  return `M${tp[0][0]} ${tp[0][1]} ${tp[1][0]} ${tp[1][1]} ${tp[2][0]} ${tp[2][1]} ${tp[3][0]} ${tp[3][1]} ${tp[0][0]} ${tp[0][1]} L${tp[1][0]} ${tp[1][1]}`;
}

function p1(size, value) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return calPoints(bp[0], tp[0], value);
}
function p2(size, value) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return calPoints(bp[3], tp[3], value);
}
function p3(size, value) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return calPoints(bp[1], tp[1], value);
}
function p4(size, value) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return calPoints(bp[2], tp[2], value);
}

let calPoints = ([x1, y1], [x2, y2], v) => {
  return [x1 + (x2 - x1) * v, y1 - (y1 - y2) * v];
};

function drawD1(size, value) {
  return `M ${p1(size, value)[0]} ${p1(size, value)[1]} L${
    p2(size, value)[0]
  } ${p2(size, value)[1]} L${p4(size, value)[0]} ${p4(size, value)[1]} L${
    p3(size, value)[0]
  } ${p3(size, value)[1]} Z`;
}

function drawD2(size, value) {
  const bp = points[`${size}b`];
  return `M ${bp[0][0]} ${bp[0][1]} L ${p1(size, value)[0]} ${
    p1(size, value)[1]
  } L${p3(size, value)[0]} ${p3(size, value)[1]} L ${bp[1][0]} ${bp[1][1]} Z `;
}
function drawD3(size, value) {
  const bp = points[`${size}b`];
  return `M ${bp[1][0]} ${bp[1][1]} L ${bp[2][0]} ${bp[2][1]}  L${
    p4(size, value)[0]
  } ${p4(size, value)[1]}L${p3(size, value)[0]} ${p3(size, value)[1]} Z`;
}

const points = {
  st: [
    [8, 3.5],
    [12, 3.5],
    [13, 2.5],
    [9, 2.5],
  ],
  sb: [
    [6, 9],
    [14, 9],
    [16, 7],
    [8, 7],
  ],
  mt: [
    [9, 4],
    [14, 4],
    [15.5, 2.5],
    [10.5, 2.5],
  ],
  mb: [
    [6, 10],
    [16, 10],
    [19, 7],
    [9, 7],
  ],
  lt: [
    [11, 4],
    [16, 4],
    [17.5, 2.5],
    [12.5, 2.5],
  ],
  lb: [
    [6, 10],
    [18, 10],
    [23, 7],
    [11, 7],
  ],
};

const data = {
  value: 1,
  theme: "blue",
  size,
};

const themeOptions = {
  0: "blue",
  1: "orange",
  2: "red",
};

// tempwarn 0 蓝色 1 橙色 2 红色
// stocknum 实时库存数据量
// maxstocknum 最大库存数据量
let maxstocknum = 1;
function formatData(d = []) {
  // 先把最大库存拿出来
  d.forEach((item) => {
    if (item.column === "maxstocknum") {
      maxstocknum = item.value;
    }
  });

  d.forEach((item) => {
    if (item.column === "stocknum") {
      data.value = Math.min(1, item.value / maxstocknum);
    }
    if (item.column === "tempwarn") {
      data.theme = themeOptions[+item.value];
    }
  });
  return data;
}

function update(d = []) {
  const { value, theme } = formatData(d);

  console.log("value", size, value, drawD1(size, value));
  container.select("svg").attr("viewBox", vb(size));
  svg
    .selectAll(`.inner`)
    .style("stroke", "#fff")
    .style("stroke-width", "0.1px")
    .style("stroke-linecap", "round")
    .style("stroke-linejoin", "round")
    .attr("d", drawD1(size, value))
    .style("fill", ColorByTheme[theme][0]);

  svg
    .selectAll(`.inner2`)
    .style("stroke", "#fff")
    .style("stroke-width", "0.1px")
    .style("stroke-linecap", "round")
    .style("stroke-linejoin", "round")
    .attr("d", drawD2(size, value))
    .style("fill", ColorByTheme[theme][1]);

  svg
    .selectAll(`.inner3`)
    .style("stroke", "#fff")
    .style("stroke-width", "0.1px")
    .style("stroke-linecap", "round")
    .style("stroke-linejoin", "round")
    .attr("d", drawD3(size, value))
    .style("fill", ColorByTheme[theme][2]);

  svg
    .selectAll(`.outer1`)
    .attr("d", outer1D(size))
    .style("fill", "#fff")
    .style("fill-opacity", "0.5")
    .style("stroke", "#fff")
    .style("stroke-opacity", "0.4")
    .style("stroke-width", "0.1px")
    .style("stroke-linecap", "round")
    .style("stroke-linejoin", "round");

  svg
    .selectAll(`.outer2`)
    .attr("d", outer2D(size))
    .style("fill", "#fff")
    .style("fill-opacity", "0.5")
    .style("stroke", "#fff")
    .style("stroke-opacity", "0.4")
    .style("stroke-width", "0.1px")
    .style("stroke-linecap", "round")
    .style("stroke-linejoin", "round");
}

return {
  draw,
  update,
};
