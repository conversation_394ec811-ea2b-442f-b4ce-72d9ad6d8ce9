/**
 * 初始化图形界面。
 * @param {HTMLElement} container - 容器元素。
 * @param {string} name - 名称。
 * @param {number} value - 初始值，0-1之间的数值，默认为0。
 * @param {string} theme - 内部填充颜色，默认为"green"，支持"green"、"red"、"blue"、"orange"。
 * @param {string} size - 尺寸，默认为"m"，支持"s"、"m"、"l"。
 * @param {number} width - 宽度，默认为200。
 * @param {number} height - 高度，默认为200。
 */
function initYard(
  container,
  name,
  value = 0,
  theme = "green",
  size = "m",
  width = 200,
  height = 200
) {
  var svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  svg.setAttribute("width", width);
  svg.setAttribute("height", height);
  svg.setAttribute("viewBox", vb(size));

  var path = document.createElementNS("http://www.w3.org/2000/svg", "path");
  path.setAttribute("d", d1(size, value));
  path.setAttribute("class", `inner inner1 yard-${theme}`);
  path.style.stroke = "#fff";
  path.style.strokeWidth = "0.1px";
  path.style.strokeLinecap = "round";
  path.style.strokeLinejoin = "round";
  svg.appendChild(path);

  var path2 = document.createElementNS("http://www.w3.org/2000/svg", "path");
  path2.setAttribute("d", outer1D(size));
  path2.setAttribute("class", `outer outer1`);
  path2.style.fill = "#fff";
  path2.style.fillOpacity = "0.5";
  path2.style.stroke = "#fff";
  path2.style.strokeOpacity = "0.4";
  path2.style.strokeWidth = "0.1px";
  path2.style.strokeLinecap = "round";
  path2.style.strokeLinejoin = "round";
  svg.appendChild(path2);

  var path3 = document.createElementNS("http://www.w3.org/2000/svg", "path");
  path3.setAttribute("d", outer2D(size));
  path3.setAttribute("class", `outer outer2`);
  path3.style.fill = "#fff";
  path3.style.fillOpacity = "0.5";
  path3.style.stroke = "#fff";
  path3.style.strokeOpacity = "0.4";
  path3.style.strokeWidth = "0.1px";
  path3.style.strokeLinecap = "round";
  path3.style.strokeLinejoin = "round";
  svg.appendChild(path3);

  var path4 = document.createElementNS("http://www.w3.org/2000/svg", "path");
  path4.setAttribute("d", d2(size, value));
  path4.setAttribute("class", `inner inner2 yard-${theme}`);
  path.style.stroke = "#fff";
  path.style.strokeWidth = "0.1px";
  path.style.strokeLinecap = "round";
  path.style.strokeLinejoin = "round";
  svg.appendChild(path4);

  var path5 = document.createElementNS("http://www.w3.org/2000/svg", "path");
  path5.setAttribute("d", d3(size, value));
  path5.setAttribute("class", `inner inner3 yard-${theme}`);
  path.style.stroke = "#fff";
  path.style.strokeWidth = "0.1px";
  path.style.strokeLinecap = "round";
  path.style.strokeLinejoin = "round";
  svg.appendChild(path5);

  var text = document.createElementNS("http://www.w3.org/2000/svg", "text");
  text.setAttribute("x", textPosition(size)[0]);
  text.setAttribute("y", textPosition(size)[1]);
  text.style.fill = "#fff";
  text.style.fontSize = "2px";
  text.style.textAnchor = "middle";
  text.innerHTML = name;
  svg.appendChild(text);

  container.appendChild(svg);

  renderLink();
}

function vb(size) {
  return size === "s"
    ? "6 2.5 10 7.5"
    : size === "m"
    ? "6 2.5 13 7.5"
    : "6 2.5 17 7.5";
}

function textPosition(size) {
  return size === "s" ? [10, 8.5] : size === "m" ? [11, 9.5] : [12.5, 9.5];
}

function outer1D(size) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return `M${bp[0][0]} ${bp[0][1]} ${tp[0][0]} ${tp[0][1]} ${tp[1][0]} ${tp[1][1]} ${bp[1][0]} ${bp[1][1]} ${bp[0][0]} ${bp[0][1]}M${bp[1][0]} ${bp[1][1]} ${tp[1][0]} ${tp[1][1]} ${tp[2][0]} ${tp[2][1]} ${bp[2][0]} ${bp[2][1]} ${bp[1][0]} ${bp[1][1]}M${bp[0][0]} ${bp[0][1]} ${bp[3][0]} ${bp[3][1]}M${bp[2][0]} ${bp[2][1]} ${bp[3][0]} ${bp[3][1]}M${tp[3][0]} ${tp[3][1]} ${bp[3][0]} ${bp[3][1]}M${tp[0][0]} ${tp[0][1]} ${tp[3][0]} ${tp[3][1]} ${tp[2][0]} ${tp[2][1]} ${tp[3][0]} ${tp[3][1]}`;
}

function outer2D(size) {
  const tp = points[`${size}t`];
  return `M${tp[0][0]} ${tp[0][1]} ${tp[1][0]} ${tp[1][1]} ${tp[2][0]} ${tp[2][1]} ${tp[3][0]} ${tp[3][1]} ${tp[0][0]} ${tp[0][1]}M${tp[1][0]} ${tp[1][1]}`;
}

function p1(size, value) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return calPoints(bp[0], tp[0], value);
}
function p2(size, value) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return calPoints(bp[3], tp[3], value);
}
function p3(size, value) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return calPoints(bp[1], tp[1], value);
}
function p4(size, value) {
  const tp = points[`${size}t`];
  const bp = points[`${size}b`];
  return calPoints(bp[2], tp[2], value);
}

let calPoints = ([x1, y1], [x2, y2], v) => {
  return [x1 + (x2 - x1) * v, y1 - (y1 - y2) * v];
};

function d1(size, value) {
  return `M ${p1(size, value)[0]} ${p1(size, value)[1]} L${
    p2(size, value)[0]
  } ${p2(size, value)[1]} L${p4(size, value)[0]} ${p4(size, value)[1]} L${
    p3(size, value)[0]
  } ${p3(size, value)[1]} Z`;
}

function d2(size, value) {
  const bp = points[`${size}b`];
  return `M ${bp[0][0]} ${bp[0][1]} L ${p1(size, value)[0]} ${
    p1(size, value)[1]
  } L${p3(size, value)[0]} ${p3(size, value)[1]} L ${bp[1][0]} ${bp[1][1]} Z `;
}
function d3(size, value) {
  const bp = points[`${size}b`];
  return `M ${bp[1][0]} ${bp[1][1]} L ${bp[2][0]} ${bp[2][1]}  L${
    p4(size, value)[0]
  } ${p4(size, value)[1]}L${p3(size, value)[0]} ${p3(size, value)[1]} Z`;
}

const points = {
  st: [
    [8, 3.5],
    [12, 3.5],
    [13, 2.5],
    [9, 2.5],
  ],
  sb: [
    [6, 9],
    [14, 9],
    [16, 7],
    [8, 7],
  ],
  mt: [
    [9, 4],
    [14, 4],
    [15.5, 2.5],
    [10.5, 2.5],
  ],
  mb: [
    [6, 10],
    [16, 10],
    [19, 7],
    [9, 7],
  ],
  lt: [
    [11, 4],
    [16, 4],
    [17.5, 2.5],
    [12.5, 2.5],
  ],
  lb: [
    [6, 10],
    [18, 10],
    [23, 7],
    [11, 7],
  ],
};

function renderLink() {
  var inners = document.getElementsByClassName("inner yard-green");
  if (inners) {
    for (var i = 0; i < inners.length; i++) {
      inners[i].style.fill = "#00896b";
    }
  }
  var inner1s = document.getElementsByClassName("inner1 yard-green");
  if (inner1s) {
    for (var i = 0; i < inner1s.length; i++) {
      inner1s[i].style.fill = "#39cda6";
    }
  }
  var inner3s = document.getElementsByClassName("inner3 yard-green");
  if (inner3s) {
    for (var i = 0; i < inner3s.length; i++) {
      inner3s[i].style.fill = "#005545";
    }
  }

  var innersOrange = document.getElementsByClassName("inner yard-orange");
  if (innersOrange) {
    for (var i = 0; i < innersOrange.length; i++) {
      innersOrange[i].style.fill = "#ec8e08";
    }
  }
  var inner1sOrange = document.getElementsByClassName("inner1 yard-orange");
  if (inner1sOrange) {
    for (var i = 0; i < inner1sOrange.length; i++) {
      inner1sOrange[i].style.fill = "#f0b36d";
    }
  }
  var inner3sOrange = document.getElementsByClassName("inner3 yard-orange");
  if (inner3sOrange) {
    for (var i = 0; i < inner3sOrange.length; i++) {
      inner3sOrange[i].style.fill = "#af6816";
    }
  }

  var innersRed = document.getElementsByClassName("inner yard-red");
  if (innersRed) {
    for (var i = 0; i < innersRed.length; i++) {
      innersRed[i].style.fill = "#d01a38";
    }
  }
  var inner1sRed = document.getElementsByClassName("inner1 yard-red");
  if (inner1sRed) {
    for (var i = 0; i < inner1sRed.length; i++) {
      inner1sRed[i].style.fill = "#e75454";
    }
  }
  var inner3sRed = document.getElementsByClassName("inner3 yard-red");
  if (inner3sRed) {
    for (var i = 0; i < inner3sRed.length; i++) {
      inner3sRed[i].style.fill = "#970404";
    }
  }

  var innersBlue = document.getElementsByClassName("inner yard-blue");
  if (innersBlue) {
    for (var i = 0; i < innersBlue.length; i++) {
      innersBlue[i].style.fill = "#61bbfb";
    }
  }
  var inner1sBlue = document.getElementsByClassName("inner1 yard-blue");
  if (inner1sBlue) {
    for (var i = 0; i < inner1sBlue.length; i++) {
      inner1sBlue[i].style.fill = "#396e98";
    }
  }
  var inner3sBlue = document.getElementsByClassName("inner3 yard-blue");
  if (inner3sBlue) {
    for (var i = 0; i < inner3sBlue.length; i++) {
      inner3sBlue[i].style.fill = "#0c7dd8";
    }
  }
}
