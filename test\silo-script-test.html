<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Silo Script 图形展示</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #333;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        padding: 0;
      }
      .container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
      }
      .silo-container {
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 10px;
        padding: 20px;
        text-align: center;
      }
      .silo-title {
        color: white;
        margin-bottom: 10px;
      }
      button {
        margin: 5px;
        padding: 5px 10px;
        background-color: #555;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
      }
      button:hover {
        background-color: #777;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="silo-container">
        <h3 class="silo-title">Silo Script 测试</h3>
        <div id="silo-script-test"></div>
        <div>
          <button onclick="updateSilo(0.2, 'sliver')">银色 20%</button>
          <button onclick="updateSilo(0.5, 'orange')">橙色 50%</button>
          <button onclick="updateSilo(0.8, 'red')">红色 80%</button>
          <button onclick="updateSilo(1.0, 'blue')">蓝色 100%</button>
        </div>
      </div>
    </div>

    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="../silo-script.js"></script>
    <script>
      let siloScript;
      
      // 页面加载完成后初始化图形
      document.addEventListener("DOMContentLoaded", function () {
        const container = d3.select("#silo-script-test");
        siloScript = (function() {
          // 这里需要包装 silo-script.js 的内容，因为它返回一个对象
          // 暂时先创建一个简单的测试
          return {
            draw: function(container, nodeInfo) {
              console.log("Drawing silo with D3.js style");
              // 这里应该调用 silo-script.js 中的 draw 函数
            },
            update: function(data) {
              console.log("Updating silo with data:", data);
              // 这里应该调用 silo-script.js 中的 update 函数
            }
          };
        })();
        
        siloScript.draw(container, {
          nodeSize: "200*200",
          nodeText: "测试设备"
        });
      });
      
      function updateSilo(value, theme) {
        const data = [
          { column: "stocknum", value: value },
          { column: "maxstocknum", value: 1 },
          { column: "tempwarn", value: theme === "sliver" ? 0 : theme === "orange" ? 1 : 2 }
        ];
        siloScript.update(data);
      }
    </script>
  </body>
</html>
