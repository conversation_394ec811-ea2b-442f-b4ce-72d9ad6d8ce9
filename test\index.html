<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Yard 图形展示</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #333;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        padding: 0;
      }
      .container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
      }
      .yard-container {
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 10px;
        padding: 20px;
        text-align: center;
      }
      .yard-title {
        color: white;
        margin-bottom: 10px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="yard-container">
        <h3 class="yard-title">绿色主题 (小)</h3>
        <div id="yard-green-small"></div>
      </div>

      <div class="yard-container">
        <h3 class="yard-title">蓝色主题 (中)</h3>
        <div id="yard-blue-medium"></div>
      </div>

      <div class="yard-container">
        <h3 class="yard-title">橙色主题 (中)</h3>
        <div id="yard-orange-medium"></div>
      </div>

      <div class="yard-container">
        <h3 class="yard-title">红色主题 (大)</h3>
        <div id="yard-red-large"></div>
      </div>
    </div>

    <script src="yard.js"></script>
    <script>
      // 页面加载完成后初始化所有图形
      document.addEventListener("DOMContentLoaded", function () {
        // 初始化小尺寸绿色主题图形，值为0.3
        initYard(
          document.getElementById("yard-green-small"),
          "设备A",
          0.3,
          "green",
          "s"
        );

        // 初始化中尺寸蓝色主题图形，值为0.6
        initYard(
          document.getElementById("yard-blue-medium"),
          "设备B",
          0.6,
          "blue",
          "m"
        );

        // 初始化中尺寸橙色主题图形，值为0.5
        initYard(
          document.getElementById("yard-orange-medium"),
          "设备D",
          0.5,
          "orange",
          "m"
        );
      });
      // 初始化大尺寸红色主题图形，值为0.8
      initYard(
        document.getElementById("yard-red-large"),
        "设备C",
        0.8,
        "red",
        "l"
      );
    </script>
  </body>
</html>
